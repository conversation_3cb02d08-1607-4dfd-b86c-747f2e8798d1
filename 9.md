# Excel日志转换器 - 科陆流水线运维日志处理系统

一个功能强大的基于Web的Excel文件处理工具，专门用于科陆流水线运维日志数据的智能化处理和分析。支持大文件处理、日期过滤、数据预览和批量导出等高级功能。

## ✨ 核心功能

### 📁 文件处理
- **智能上传**: 支持拖拽上传和点击选择，最大支持50MB文件
- **格式支持**: 专门优化的`.xlsx`文件处理
- **进度显示**: 实时显示文件处理进度和状态
- **错误处理**: 完善的错误提示和异常处理机制

### 📅 日期管理
- **月历视图**: 直观的月历界面显示数据分布
- **日期过滤**: 灵活选择特定日期范围的数据
- **批量选择**: 支持多日期选择和批量操作
- **统计信息**: 实时显示记录总数和日期范围

### � 数据处理
- **智能转换**: 按照预定义规则自动处理Excel数据
- **数据预览**: 处理前可预览数据内容和结构
- **格式化输出**: 生成符合要求的运维日志格式
- **自动命名**: 智能生成文件名：`科陆流水线运维日志YYYYMMDD.xlsx`

### ⚡ 性能优化
- **内存管理**: 智能内存管理和垃圾回收机制
- **批量处理**: 大数据集的分批处理能力
- **异步操作**: 非阻塞的文件处理流程
- **Web Workers**: 后台处理支持（可选）

## 🚀 使用方法

### 基本操作流程
1. **打开应用**: 在浏览器中打开 `index.html` 文件
2. **上传文件**:
   - 点击"选择文件"按钮选择Excel文件
   - 或直接拖拽`.xlsx`文件到页面任意位置
3. **查看数据**: 文件上传后自动显示月历视图和数据统计
4. **选择日期**: 在月历中点击选择需要导出的日期
5. **预览数据**: 点击"预览数据"查看选中日期的数据内容
6. **导出文件**: 点击"导出Excel"生成并下载处理后的文件

### 高级功能
- **月份导航**: 使用左右箭头切换不同月份
- **清除选择**: 一键清除所有已选日期
- **重新处理**: 对同一文件进行重新处理

## 🔧 数据转换规则

### 列处理规则
- ✅ **保留列**: 保留所有原始数据列（除"记录人"列）
- ❌ **移除列**: 自动移除"记录人"列
- ➕ **新增列**:
  - "备注"列（默认值："已解决"）
  - "维护保养情况"列（空白，供后续填写）

### 文件命名规则
- 格式：`科陆流水线运维日志YYYYMMDD.xlsx`
- 根据输出时间命名

## 🛠️ 技术架构

### 前端技术栈
- **HTML5**: 现代化页面结构和语义化标签
- **CSS3**: 响应式设计和现代化UI样式
- **JavaScript (ES6+)**: 模块化的核心业务逻辑
- **ExcelJS**: 专业的Excel文件处理库